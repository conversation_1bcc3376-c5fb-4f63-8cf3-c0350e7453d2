<div align="center">
    
<img src="https://github.com/user-attachments/assets/c9fc6bd3-daae-402d-8eeb-828473ede8d4" style="width: 700px; height: auto;" >

<!-- https://github.com/user-attachments/assets/c9eeaf74-6649-4810-b420-e2c4ad4bd365 -->


<br>

| :exclamation: | This project is part of the [FadSec Lab suite](https://github.com/fadsec-lab). <br> Discover our focus on ad-free, privacy-first applications and stay updated on future releases!  |
|---------------|:---------------------------------------------------------------------------------------------------------------------------------------------------------------|




---

<img src="https://github.com/user-attachments/assets/c730eda3-5887-458d-8df1-971a74807b73" style="width: 100px; height: auto;" >

# FadCrypt

**Advanced and elegant Windows app encryption – powerful, customizable, open-source, and completely free!**

[![GitHub all releases](https://img.shields.io/github/downloads/anonfaded/FadCrypt/total?label=Downloads&logo=github)](https://github.com/anonfaded/FadCrypt/releases/)

[![ko-fi badge](https://img.shields.io/badge/buy_me_a-coffee-red)](https://ko-fi.com/D1D510FNSV)
[![Discord](https://img.shields.io/discord/1263384048194027520?label=Join%20Us%20on%20Discord&logo=discord)](https://discord.gg/kvAZvdkuuN )

<!-- <img alt="Discord" src="https://img.shields.io/discord/1263384048194027520?style=social&logo=discord&label=Join%20chat&color=red"> -->



<br>
<br>

</div>

<p align="center">
        <img src="https://raw.githubusercontent.com/bornmay/bornmay/Update/svg/Bottom.svg" alt="Github Stats" />
</p>

---

<details>
    <summary>Expand Table of Contents</summary>
    
<br>
        

- [FadCrypt](#fadcrypt)
  - [📱 Screenshots](#-screenshots)
  - [How FadCrypt Works:](#how-fadcrypt-works)
  - [⬇️ Download](#️-download)
  - [Features:](#features)
  - [Featured On](#featured-on)
  - [Join Community](#join-community)
  - [Support](#support)
  - [Contributions](#contributions)
    - [How to Contribute](#how-to-contribute)
- [Install Dependencies \& Build](#install-dependencies--build)
- [Reset Password](#reset-password)
</details>

---

## 📱 Screenshots

<div align="center">
<!--     <img src="https://github.com/anonfaded/FadCam/assets/124708903/4a93c111-fc67-4d75-94b1-fa4e01822998" style="width: 50px; height: auto;" >
    <br>
    <em>apk icon</em> -->
    <br><br>
    <img src="https://github.com/user-attachments/assets/b81daec5-8c0f-49f0-9cac-bec61d303eef" style="width: 500px; height: auto;" >
    <img src="https://github.com/user-attachments/assets/df93ac6d-d8eb-45e7-b150-3a1e6d6a80c2" style="width: 500px; height: auto;" >
    <img src="https://github.com/user-attachments/assets/28db5d03-0b08-47fa-bdc6-01244947c124" style="width: 500px; height: auto;" >
    <img src="https://github.com/user-attachments/assets/01e1a2b1-8cdf-40a2-95e0-41109c07db5c" style="width: 500px; height: auto;" >
    <img src="https://github.com/user-attachments/assets/bcbf1b09-6920-46fb-8c3d-b475536060a0" style="width: 500px; height: auto;" >
    <img src="https://github.com/user-attachments/assets/b016d43d-0105-46b5-b2eb-5c697230fcd8" style="width: 500px; height: auto;" >
    <img src="https://github.com/user-attachments/assets/ec7dcc78-2a36-42ef-81a3-8cdda3e33195" style="width: 500px; height: auto;" >
 <br>






<!--     <br> -->
<!--     <em>UI</em> -->
   
</div>
<!--     <details>
        <summary><strong>More Screenshots</strong></summary>
        <img src="/img/3.png" style="width: 700px; height: auto;" >
        <br>
        <img src="/img/4.png" style="width: 700px; height: auto;" >
        <br>
        <img src="/img/5.png" style="width: 700px; height: auto;" >
    </details> -->
    
## How FadCrypt Works:

1. **Password Creation:** When you set a password, it's encrypted and saved with the configuration file of locked apps. During monitoring, these files are backed up to `C:\ProgramData\FadCrypt\Backup\`. If detected as deleted, they are automatically recovered and restored.

2. **Monitoring Mode:** Press "Start Monitoring" to set FadCrypt as a startup app. It will automatically activate every time your PC starts, and will persistently run unless you press "Stop Monitoring."

3. **Security Features:** FadCrypt can't be stopped without the correct password. The app also disables Control Panel, Registry Editor, Task Manager, and msconfig to prevent tampering.

4. **Mutex Protection:** FadCrypt uses mutual exclusion to ensure only one instance runs at a time, blocking new instances until the current one is closed with the password. This prevents bypass attempts.

**Note:** The password recovery feature is not available yet.



## ⬇️ Download

Download the latest `windows setup installer` file directly from the [releases page](https://github.com/anonfaded/FadCrypt/releases).

[<img src="https://raw.githubusercontent.com/vadret/android/master/assets/get-github.png" alt="Get it on GitHub" height="70">](https://github.com/anonfaded/FadCrypt/releases)

## Features:

- **Application Locking:** Secure apps with an encrypted password; it can't be recovered if lost nor the tool can be stopped.
- **Real-time Monitoring:** Detects and auto-recovers critical files if deleted.
- **Auto-Startup:** Automatically enabled for every session after starting monitoring.
- **Aesthetic UI:** Choose custom wallpapers or a minimal style with smooth animations.
  
**Security:**
- **System Tools Disabled:** Disables Command Prompt, Task Manager, msconfig, Control Panel, and Registry Editor; a nightmare for a layman to bypass. (Manual PowerShell disabling is recommended for maximum security.)
- **Encrypted Storage:** Password and config data are encrypted and backed up somewhere in `C://` drive.

**Testing:**
- **Testing Blocked Tools:** Confirm effectiveness by searching for Control Panel or Task Manager and try to open it during monitoring.

**Extras:**
- **Snake Game:** Enjoy the classic Snake game on the main tab or from the tray icon! :)

**Upcoming Features:**
- **Password Recovery:** Recover forgotten passwords with ease.
- **Logging and Alerts:** Includes screenshots, email alerts on wrong password attempts, and detailed logs.
- **Community Input:** Integrates feedback for improved security and usability.

## Featured On
- [VPN Club on Telegram](https://t.me/s/wbnet?q=fadcrypt)
- [popMods on Telegram](https://t.me/s/popmods?q=fadcrypt)
- [blog.csdn.net](https://blog.csdn.net/qq_29607687/article/details/141366524)
<!-- - [rhkb.cn](http://www.rhkb.cn/news/405585.html) -->

## Join Community
Join our [Discord server](https://discord.gg/kvAZvdkuuN) to share ideas, seek help, or connect with other users. Your feedback and contributions are welcome!

[![Discord](https://img.shields.io/discord/1263384048194027520?label=Join%20Us%20on%20Discord&logo=discord)](https://discord.gg/kvAZvdkuuN )


## Support

<a href='https://ko-fi.com/D1D510FNSV' target='_blank'><img height='36' style='border:0px;height:36px;' src='https://storage.ko-fi.com/cdn/kofi3.png?v=3' border='0' alt='Buy Me a Coffee at ko-fi.com' /></a>

## Contributions

We welcome any contributions to improve this project! Whether it's bug fixes or new features, your help is appreciated.

### How to Contribute
1. **Check Issues**: Browse the [issues](https://github.com/anonfaded/FadCrypt/issues) to see where you can help.
2. **Fork the Repo**: Fork the repository to make your changes.
3. **Submit a PR**: Create a pull request with a clear description of your changes.

We look forward to your contributions!

# Install Dependencies & Build

```python
pip install cryptography psutil pillow pystray watchdog tkinterdnd2 ttkbootstrap pygame requests
python -m PyInstaller FadCrypt.spec
```
# Reset Password

Follow the steps below to regain access to FadCrypt, or download the guide as a PDF for reference:  
[FadCrypt_Reset_Password_Guide.pdf](https://github.com/user-attachments/files/19832431/FadCrypt_Reset_Password_Guide.pdf)

## 1. Terminate the app processes (if running)

1. Open the search box: `Windows key + S`  
2. Type **"PowerShell"**, right-click, and select **"Run as administrator"**
3. In the PowerShell window, enter the following command to kill all running instances of FadCrypt:

```powershell
Stop-Process -Name "fadcrypt" -Force
```

## 2. Delete the password binary file  
*(This allows you to create a new password without needing the old one)*

1. Navigate to and delete the following file:

```
C:\Users\<USER>\AppData\Roaming\FadCrypt\encrypted_password.bin
```

2. Also delete the backup copy from:

```
C:\ProgramData\FadCrypt\Backup\encrypted_password.bin
```

Now you can open the app again and set a new password — it’ll work like a charm!
